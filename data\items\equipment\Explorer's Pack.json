{"desc": [], "special": [], "index": "explorers-pack", "name": "Explorer's Pack", "equipment_category": {"index": "adventuring-gear", "name": "Adventuring Gear", "url": "/api/2014/equipment-categories/adventuring-gear"}, "gear_category": {"index": "equipment-packs", "name": "Equipment Packs", "url": "/api/2014/equipment-categories/equipment-packs"}, "cost": {"quantity": 10, "unit": "gp"}, "contents": [{"item": {"index": "backpack", "name": "Backpack", "url": "/api/2014/equipment/backpack"}, "quantity": 1}, {"item": {"index": "bedroll", "name": "Bedroll", "url": "/api/2014/equipment/bedroll"}, "quantity": 1}, {"item": {"index": "mess-kit", "name": "Mess Kit", "url": "/api/2014/equipment/mess-kit"}, "quantity": 1}, {"item": {"index": "tinderbox", "name": "Tinderbox", "url": "/api/2014/equipment/tinderbox"}, "quantity": 1}, {"item": {"index": "torch", "name": "<PERSON>ch", "url": "/api/2014/equipment/torch"}, "quantity": 10}, {"item": {"index": "rations-1-day", "name": "Rations (1 day)", "url": "/api/2014/equipment/rations-1-day"}, "quantity": 10}, {"item": {"index": "waterskin", "name": "<PERSON><PERSON>", "url": "/api/2014/equipment/waterskin"}, "quantity": 1}, {"item": {"index": "rope-hempen-50-feet", "name": "Rope, hempen (50 feet)", "url": "/api/2014/equipment/rope-hempen-50-feet"}, "quantity": 1}], "url": "/api/2014/equipment/explorers-pack", "updated_at": "2025-04-08T21:13:58.828Z", "properties": []}