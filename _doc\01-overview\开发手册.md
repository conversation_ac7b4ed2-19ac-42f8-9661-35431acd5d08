# DND游戏项目文档

欢迎来到DND游戏项目！这是一个基于LLM驱动的龙与地下城RPG游戏项目。
这是一个大型项目，请认真架构好，文件应按功能分开。

## 项目概述

- **游戏类型**：单人龙与地下城RPG游戏
- **LLM驱动**：LLM担任游戏主持人，动态生成剧情和副本
- **图像增强**：每回合自动生成场景图片
- **多平台支持**：支持Web和移动设备

## 技术栈

- **前端**：Flutter
- **后端**：Python + FastAPI + LangChain
- **数据库**：存档使用JSON，策划配置使用CSV
- **AI集成**：LangChain + OpenAI/本地模型 + ComfyUI

## 开发规范
1. 所有代码文件必须遵循统一的代码风格
2. 重要功能可以在编写测试用例存放在test目录下。
3. 配置数据统一放在 data 目录下
4. 玩家数据统一存放在 players 目录下，每个玩家一个文件夹
5. 在开发过程中请不要使用代码模拟数据，这会大幅增加查错难度。如果失败可以抛出异常即可。
6. 所有配置相关的内容，请放在 backend/config.py 比如目录。
7. 使用防御性编程的方式，可以加一些断言。
8. 在游戏中使用token识别角色：token由 玩家名_角色uuid组成
9. 注意前端我们要支持移动设备和网页版本。
10. 这是一个可以布署在网站的。可以支持多人同时在线的服务。
11. 使用后端的API来获取玩家及游戏相关数据。
12. 请使用统一的日志系统（写在一个文件中），开发期调试模式，日志也同步输出在控制台上。（日志要有文件名，行号）
13. 请不要自行开启服务器，如果有需要让我来开启。
14. 请不要在异常的时候，使用模拟数据，只要输出异常即可


前端，应有详细的日志，进入界面，每一个界面被调用时，界面的功能被点击时，应输出日志，方便调试