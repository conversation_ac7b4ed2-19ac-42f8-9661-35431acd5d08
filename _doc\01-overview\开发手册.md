# DND游戏项目开发手册

欢迎来到DND游戏项目！这是一个基于LLM驱动的龙与地下城RPG游戏项目。
这是一个大型项目，请认真架构好，文件应按功能分开。

## 项目概述

- **游戏类型**：多人在线龙与地下城RPG游戏
- **LLM驱动**：LLM担任游戏主持人，动态生成剧情和副本
- **图像增强**：每回合自动生成场景图片
- **多平台支持**：支持Web和移动设备
- **部署方式**：可部署在网站，支持多人同时在线

## 技术栈

- **前端**：Flutter（主要）+ Web前端（已弃用）
- **后端**：Python + FastAPI + LangChain
- **数据存储**：玩家存档使用JSON，游戏配置使用CSV
- **AI集成**：LangChain + OpenAI/本地模型 + ComfyUI
- **身份认证**：基于Token的角色识别系统

## 核心开发规范

### 🚫 严禁模拟数据规则
**这是最重要的开发规范，违反此规则会导致严重的调试困难！**

1. **绝对禁止在代码中使用模拟数据**
   - ❌ 不要在API失败时返回假数据
   - ❌ 不要在数据库查询失败时返回模拟结果
   - ❌ 不要在网络请求失败时使用默认值
   - ✅ 直接抛出异常，让错误暴露出来

2. **正确的错误处理方式**
   ```python
   # ❌ 错误示例 - 使用模拟数据
   def get_character_data(character_id):
       try:
           return load_character_from_file(character_id)
       except FileNotFoundError:
           return {"name": "测试角色", "level": 1}  # 这会隐藏真实问题！

   # ✅ 正确示例 - 抛出异常
   def get_character_data(character_id):
       try:
           return load_character_from_file(character_id)
       except FileNotFoundError as e:
           logger.error(f"角色文件不存在: {character_id}, 错误: {e}")
           raise HTTPException(status_code=404, detail=f"角色 {character_id} 不存在")
   ```

3. **前端错误处理**
   ```dart
   // ❌ 错误示例 - 使用默认数据
   Future<Character> loadCharacter(String id) async {
     try {
       return await apiService.getCharacter(id);
     } catch (e) {
       return Character.defaultCharacter(); // 隐藏了API问题！
     }
   }

   // ✅ 正确示例 - 显示错误
   Future<Character> loadCharacter(String id) async {
     try {
       return await apiService.getCharacter(id);
     } catch (e) {
       logger.error('加载角色失败: $id, 错误: $e');
       showErrorDialog('加载角色失败，请检查网络连接');
       rethrow; // 让调用者处理错误
     }
   }
   ```

### 📁 项目结构规范

4. **目录组织**
   - 配置数据统一放在 `data/` 目录下
   - 玩家数据统一存放在 `players/` 目录下，每个玩家一个文件夹
   - 所有配置相关的内容，请放在 `backend/config.py`
   - 测试用例存放在 `test/` 目录下

5. **代码组织**
   - 所有代码文件必须遵循统一的代码风格
   - 文件应按功能分开，避免单个文件过大
   - 使用防御性编程的方式，可以加一些断言

### 🔐 身份认证规范

6. **Token系统**
   - 在游戏中使用token识别角色：token由 `玩家名_角色uuid` 组成
   - 所有API请求都需要验证token的有效性
   - Token失效时必须要求重新登录，不能使用游客模式

### 🌐 多平台支持

7. **前端兼容性**
   - 前端要支持移动设备和网页版本
   - 使用响应式设计，适配不同屏幕尺寸
   - 使用后端的API来获取玩家及游戏相关数据

8. **部署要求**
   - 这是一个可以部署在网站的服务
   - 支持多人同时在线
   - 请不要自行开启服务器，如果有需要让我来开启

## 📝 日志系统规范

### 统一日志系统
9. **日志配置**
   - 使用统一的日志系统，所有日志写入同一个文件
   - 开发期调试模式，日志同步输出在控制台上
   - 日志必须包含文件名和行号信息
   - 日志文件存放在 `logs/` 目录下

10. **日志级别使用**
    ```python
    # 后端日志示例
    import logging
    from backend.logger import get_logger

    logger = get_logger(__name__)

    # DEBUG: 详细的调试信息
    logger.debug(f"处理角色数据: {character_id}")

    # INFO: 一般信息
    logger.info(f"用户 {username} 登录成功")

    # WARNING: 警告信息
    logger.warning(f"角色 {character_id} 血量过低: {hp}")

    # ERROR: 错误信息
    logger.error(f"加载角色失败: {character_id}, 错误: {e}")

    # CRITICAL: 严重错误
    logger.critical(f"数据库连接失败: {e}")
    ```

### 前端日志规范
11. **Flutter日志要求**
    - 进入界面时输出日志
    - 每个界面被调用时输出日志
    - 界面功能被点击时输出日志
    - 网络请求前后输出日志

    ```dart
    // Flutter日志示例
    import 'package:logging/logging.dart';

    final logger = Logger('ScreenName');

    class CharacterSelectionScreen extends StatefulWidget {
      @override
      _CharacterSelectionScreenState createState() {
        logger.info('进入角色选择界面');
        return _CharacterSelectionScreenState();
      }
    }

    void _onCharacterTap(String characterId) {
      logger.info('点击角色卡片: $characterId');
      // 处理点击事件
    }

    Future<void> _loadCharacters() async {
      logger.info('开始加载角色列表');
      try {
        final characters = await characterService.getCharacters();
        logger.info('角色列表加载成功，数量: ${characters.length}');
      } catch (e) {
        logger.error('角色列表加载失败: $e');
        rethrow;
      }
    }
    ```

## 🧪 测试与调试规范

### 测试用例编写
12. **测试要求**
    - 重要功能必须编写测试用例
    - 测试用例存放在 `test/` 目录下
    - 测试数据使用专门的测试数据，不要使用生产数据
    - 每个API接口都应该有对应的测试用例

### 调试最佳实践
13. **调试流程**
    - 遇到问题时，首先查看日志文件
    - 使用断言验证关键数据的正确性
    - 不要在调试时临时添加模拟数据
    - 使用调试器而不是print语句进行调试

    ```python
    # 使用断言验证数据
    def process_character_data(character_data):
        assert character_data is not None, "角色数据不能为空"
        assert 'id' in character_data, "角色数据必须包含ID"
        assert character_data['level'] > 0, "角色等级必须大于0"

        logger.debug(f"处理角色数据: {character_data['id']}")
        # 处理逻辑...
    ```

## 🔧 开发工具与环境

### 开发环境设置
14. **环境要求**
    - Python 3.8+ 用于后端开发
    - Flutter SDK 用于前端开发
    - 使用虚拟环境进行Python开发
    - 使用统一的代码格式化工具

15. **代码质量**
    - 定期运行 `flutter analyze` 检查Flutter代码质量
    - 使用 `black` 格式化Python代码
    - 使用 `pylint` 检查Python代码规范
    - 提交代码前必须通过所有测试用例

## 🚨 常见问题预防

### 数据一致性
16. **数据验证**
    - 所有用户输入都必须验证
    - API参数必须进行类型检查
    - 文件操作前检查文件是否存在
    - 数据库操作使用事务确保一致性

### 性能优化
17. **性能要求**
    - 避免在循环中进行数据库查询
    - 使用缓存减少重复计算
    - 图片资源使用适当的压缩
    - 定期清理日志文件避免磁盘空间不足

### 安全规范
18. **安全要求**
    - 所有密码必须加密存储
    - API接口必须进行权限验证
    - 用户输入必须进行SQL注入防护
    - 敏感信息不能记录在日志中

---

## 📋 开发检查清单

在提交代码前，请确保：

- [ ] 没有使用任何模拟数据
- [ ] 所有异常都有适当的错误处理
- [ ] 添加了必要的日志记录
- [ ] 编写了相应的测试用例
- [ ] 代码通过了格式检查
- [ ] 更新了相关文档
- [ ] 测试了多平台兼容性

---

*最后更新：2025-06-14*