{"index": "flexible-casting-converting-spell-slot", "class": {"index": "sorcerer", "name": "Sorcerer", "url": "/api/2014/classes/sorcerer"}, "name": "Flexible Casting: Converting Spell Slot", "level": 2, "prerequisites": [], "desc": ["As a bonus action on your turn, you can expend one spell slot and gain a number of sorcery points equal to the slot's level.."], "url": "/api/2014/features/flexible-casting-converting-spell-slot", "updated_at": "2025-04-08T21:14:01.855Z"}